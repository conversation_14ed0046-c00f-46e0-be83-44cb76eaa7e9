// constants.dart
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

// 색상
extension LDColors on Color {
  static Color get mainLime => Color.fromARGB(255, 214, 237, 23);
  static Color get foundationLimeDark => Color.fromARGB(255, 128, 142, 14);
  static Color get foundationLimeLight => Color.fromARGB(255, 242, 249, 183);
  static Color get foundationLime => Color.fromARGB(255, 171, 190, 18);
  static Color get mainRed => Color.fromARGB(255, 245, 53, 53);
  static Color get mainBlue => Color.fromARGB(255, 23, 81, 208);
  static Color get mainGrey => Color.fromARGB(255, 96, 96, 96);
  static Color get darkGrey => Color.fromARGB(255, 58, 58, 58);
  static Color get darkHoverGrey => Color.fromARGB(255, 150, 151, 151);
  static Color get mainWhiteGrey => Color.fromARGB(255, 249, 249, 249);
  static Color get lightGrey => Color.fromARGB(255, 239, 239, 239);
  static Color get foundationGrey => Color.fromARGB(255, 206, 206, 206);
  static Color get subGrey => Color.fromARGB(255, 206, 206, 206);
}

// 여백
final double defaultPadding = Adaptive.sp(15);

// 텍스트 스타일
const TextStyle headingStyle = TextStyle(
  fontSize: 24,
  fontWeight: FontWeight.bold,
  color: Colors.black,
);

bool isMobile(BuildContext context) {
  final screenSize = MediaQuery.of(context).size;
  return screenSize.width < 600; // 600px 이하를 모바일로 간주
}

bool isWideScreen(BuildContext context) {
  final screenSize = MediaQuery.of(context).size;
  return screenSize.width > 800; // 1200px 이상을 와이드 스크린으로 간주
}

// Feature Flag
const bool showSubscription = false;
const bool showInAppReview = false;
const bool showStreakReview = true;
const bool showPractice = false;

// English word lists for random nickname generation
final List<String> engAdjectiveEmotion = [
  "Happy",
  "Brave",
  "Clever",
  "Gentle",
  "Jolly",
  "Lucky",
  "Mighty",
  "Noble",
  "Proud",
  "Quiet",
  "Swift",
  "Wise",
  "Calm",
  "Eager",
  "Fierce",
  "Kind",
  "Lively",
  "Merry",
  "Neat",
  "Polite",
  "Quick",
  "Silly",
  "Tiny",
  "Witty",
  "Awesome",
  "Bold",
  "Bright",
  "Cheerful",
  "Daring",
  "Elegant",
  "Friendly",
  "Graceful",
  "Honest",
  "Joyful",
  "Loyal",
  "Magical",
  "Peaceful",
  "Radiant",
  "Sincere",
  "Talented",
  "Unique",
  "Vibrant",
  "Warm",
  "Zealous"
];

final List<String> engNounAnimal = [
  "Cat",
  "Dog",
  "Bear",
  "Fox",
  "Wolf",
  "Lion",
  "Tiger",
  "Eagle",
  "Hawk",
  "Deer",
  "Panda",
  "Koala",
  "Rabbit",
  "Mouse",
  "Dolphin",
  "Whale",
  "Penguin",
  "Elephant",
  "Monkey",
  "Zebra",
  "Giraffe",
  "Turtle",
  "Frog",
  "Bird",
  "Owl",
  "Squirrel",
  "Raccoon",
  "Hedgehog",
  "Beaver",
  "Otter",
  "Badger",
  "Cheetah",
  "Jaguar",
  "Leopard",
  "Lynx",
  "Panther",
  "Gazelle",
  "Antelope",
  "Bison",
  "Buffalo",
  "Camel",
  "Kangaroo",
  "Platypus",
  "Armadillo"
];
